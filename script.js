// Domino Game Implementation
class DominoGame {
    constructor() {
        this.tiles = this.generateTiles();
        this.playerHand = [];
        this.aiHand = [];
        this.boneyard = [];
        this.board = [];
        this.currentPlayer = 'player'; // 'player' or 'ai'
        this.gameStarted = false;
        this.playerScore = 0;
        this.aiScore = 0;
        
        this.initializeGame();
        this.setupEventListeners();
    }

    // Generate all 28 domino tiles
    generateTiles() {
        const tiles = [];
        let tileId = 1;
        
        for (let i = 0; i <= 6; i++) {
            for (let j = i; j <= 6; j++) {
                tiles.push({
                    id: tileId,
                    left: i,
                    right: j,
                    image: `images/tile_${tileId}.png`
                });
                tileId++;
            }
        }
        
        return this.shuffleArray(tiles);
    }

    // Shuffle array using Fisher-Yates algorithm
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    // Initialize new game
    initializeGame() {
        this.tiles = this.generateTiles();
        this.playerHand = this.tiles.splice(0, 7);
        this.aiHand = this.tiles.splice(0, 7);
        this.boneyard = [...this.tiles];
        this.board = [];
        this.currentPlayer = 'player';
        this.gameStarted = false;
        
        this.renderPlayerHand();
        this.renderAIHand();
        this.renderBoneyard();
        this.renderBoard();
        this.updateUI();
    }

    // Setup event listeners
    setupEventListeners() {
        document.getElementById('new-game-btn').addEventListener('click', () => {
            this.initializeGame();
        });

        document.getElementById('pass-turn-btn').addEventListener('click', () => {
            this.passTurn();
        });

        document.getElementById('hint-btn').addEventListener('click', () => {
            this.showHint();
        });

        // Drag and drop setup will be added in renderPlayerHand
    }

    // Render player's hand
    renderPlayerHand() {
        const playerHandElement = document.getElementById('player-hand');
        playerHandElement.innerHTML = '';

        this.playerHand.forEach((tile, index) => {
            const tileElement = this.createTileElement(tile, 'player', index);
            playerHandElement.appendChild(tileElement);
        });

        // Update tile count
        const playerCountElement = document.getElementById('player-tile-count');
        if (playerCountElement) {
            playerCountElement.textContent = this.playerHand.length;
        }
    }

    // Render AI's hand (as back tiles)
    renderAIHand() {
        const aiHandElement = document.getElementById('ai-hand');
        aiHandElement.innerHTML = '';

        this.aiHand.forEach(() => {
            const tileElement = document.createElement('div');
            tileElement.className = 'domino-tile back';
            tileElement.style.backgroundImage = 'none';
            aiHandElement.appendChild(tileElement);
        });

        // Update tile count
        const aiCountElement = document.getElementById('ai-tile-count');
        if (aiCountElement) {
            aiCountElement.textContent = this.aiHand.length;
        }
    }

    // Render boneyard
    renderBoneyard() {
        const boneyardElement = document.getElementById('boneyard');
        const boneyardCountElement = document.getElementById('boneyard-count');
        
        boneyardElement.innerHTML = '';
        boneyardCountElement.textContent = this.boneyard.length;

        // Show first few tiles as back tiles
        const tilesToShow = Math.min(this.boneyard.length, 10);
        for (let i = 0; i < tilesToShow; i++) {
            const tileElement = document.createElement('div');
            tileElement.className = 'domino-tile back';
            boneyardElement.appendChild(tileElement);
        }
    }

    // Create tile element
    createTileElement(tile, owner, index) {
        const tileElement = document.createElement('div');
        tileElement.className = 'domino-tile';
        tileElement.style.backgroundImage = `url(${tile.image})`;
        tileElement.dataset.tileId = tile.id;
        tileElement.dataset.owner = owner;
        tileElement.dataset.index = index;

        if (owner === 'player') {
            this.setupDragAndDrop(tileElement, tile);
        }

        return tileElement;
    }

    // Setup drag and drop for player tiles
    setupDragAndDrop(tileElement, tile) {
        tileElement.draggable = true;
        
        tileElement.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', JSON.stringify({
                tileId: tile.id,
                left: tile.left,
                right: tile.right
            }));
            tileElement.classList.add('dragging');
        });

        tileElement.addEventListener('dragend', () => {
            tileElement.classList.remove('dragging');
        });
    }

    // Render game board
    renderBoard() {
        const boardElement = document.getElementById('game-board');

        if (this.board.length === 0) {
            boardElement.innerHTML = '<div class="board-center"><div class="start-message">اسحب قطعة دومينو هنا لبدء اللعبة</div></div>';
            this.setupBoardDropZone(boardElement);
            return;
        }

        boardElement.innerHTML = '';

        this.board.forEach((tile, index) => {
            const tileElement = this.createTileElement(tile, 'board', index);
            tileElement.classList.add('placed');

            // Smart rotation logic: tiles with different faces are placed horizontally
            const shouldRotate = this.shouldTileBeHorizontal(index);
            if (shouldRotate) {
                tileElement.classList.add('horizontal');
                // Add flipped class if the tile was flipped to match connections
                if (tile.flipped) {
                    tileElement.classList.add('flipped');
                }
                // Add a subtle indicator that this tile was rotated for clarity
                const originalNums = tile.originalLeft !== undefined ?
                    `${tile.originalLeft}-${tile.originalRight}` : `${tile.left}-${tile.right}`;
                tileElement.title = `قطعة ${originalNums} (موضوعة بالعرض لوضوح الأرقام المختلفة)`;
            } else {
                const originalNums = tile.originalLeft !== undefined ?
                    `${tile.originalLeft}-${tile.originalRight}` : `${tile.left}-${tile.right}`;
                tileElement.title = `قطعة ${originalNums}`;
            }

            // Add some spacing and positioning
            tileElement.style.margin = '2px';
            tileElement.style.zIndex = index;

            // Add visual connection indicators
            if (index > 0) {
                tileElement.style.position = 'relative';
            }

            boardElement.appendChild(tileElement);
        });

        this.setupBoardDropZone(boardElement);
    }

    // Determine if a tile should be placed horizontally
    shouldTileBeHorizontal(index) {
        // Get the tile at this index
        const tile = this.board[index];
        if (!tile) return false;

        // Use original values to determine if it's a double
        const originalLeft = tile.originalLeft || tile.left;
        const originalRight = tile.originalRight || tile.right;

        // If the tile has different faces (not a double like 1-1, 2-2, etc.),
        // place it horizontally to clearly show both different numbers
        // Doubles (same numbers on both sides) look better vertically
        return originalLeft !== originalRight;
    }

    // Setup board as drop zone
    setupBoardDropZone(boardElement) {
        boardElement.addEventListener('dragover', (e) => {
            e.preventDefault();
            boardElement.classList.add('drop-zone');
        });

        boardElement.addEventListener('dragleave', () => {
            boardElement.classList.remove('drop-zone', 'invalid-drop');
        });

        boardElement.addEventListener('drop', (e) => {
            e.preventDefault();
            boardElement.classList.remove('drop-zone', 'invalid-drop');
            
            const tileData = JSON.parse(e.dataTransfer.getData('text/plain'));
            this.handleTileDrop(tileData);
        });
    }

    // Handle tile drop on board
    handleTileDrop(tileData) {
        if (this.currentPlayer !== 'player') return;

        const tile = this.playerHand.find(t => t.id === tileData.tileId);
        if (!tile) return;

        if (this.canPlaceTile(tile)) {
            this.placeTile(tile, 'player');
            this.removeFromPlayerHand(tile);
            this.renderPlayerHand();
            this.renderBoard();
            
            if (this.checkWinCondition()) {
                this.endGame();
                return;
            }
            
            this.switchTurn();
        } else {
            this.showMessage('لا يمكن وضع هذه القطعة هنا!');
        }
    }

    // Check if tile can be placed
    canPlaceTile(tile) {
        if (this.board.length === 0) return true;

        const leftEnd = this.board[0].left;
        const rightEnd = this.board[this.board.length - 1].right;

        return tile.left === leftEnd || tile.right === leftEnd ||
               tile.left === rightEnd || tile.right === rightEnd;
    }

    // Place tile on board
    placeTile(tile, player) {
        if (this.board.length === 0) {
            this.board.push({ ...tile, originalLeft: tile.left, originalRight: tile.right, flipped: false });
            this.gameStarted = true;
            return;
        }

        const leftEnd = this.board[0].left;
        const rightEnd = this.board[this.board.length - 1].right;

        // Create a new tile object with proper orientation
        let placedTile = { ...tile, originalLeft: tile.left, originalRight: tile.right };

        if (tile.left === rightEnd) {
            // Place normally at the right end
            placedTile.flipped = false;
            this.board.push(placedTile);
        } else if (tile.right === rightEnd) {
            // Flip tile to match right end
            placedTile.left = tile.right;
            placedTile.right = tile.left;
            placedTile.flipped = true;
            this.board.push(placedTile);
        } else if (tile.right === leftEnd) {
            // Place normally at the left end
            placedTile.flipped = false;
            this.board.unshift(placedTile);
        } else if (tile.left === leftEnd) {
            // Flip tile to match left end
            placedTile.left = tile.right;
            placedTile.right = tile.left;
            placedTile.flipped = true;
            this.board.unshift(placedTile);
        }
    }

    // Remove tile from player hand
    removeFromPlayerHand(tile) {
        const index = this.playerHand.findIndex(t => t.id === tile.id);
        if (index !== -1) {
            this.playerHand.splice(index, 1);
        }
    }

    // Switch turn between player and AI
    switchTurn() {
        this.currentPlayer = this.currentPlayer === 'player' ? 'ai' : 'player';
        this.updateUI();
        
        if (this.currentPlayer === 'ai') {
            setTimeout(() => this.aiTurn(), 1000);
        }
    }

    // AI turn logic
    aiTurn() {
        const bestMove = this.getAIMove ? this.getAIMove() : null;

        if (bestMove) {
            this.placeTile(bestMove, 'ai');
            this.removeFromAIHand(bestMove);

            this.renderAIHand();
            this.renderBoard();

            // Show message with original tile numbers
            const originalNums = bestMove.originalLeft !== undefined ?
                `${bestMove.originalLeft}-${bestMove.originalRight}` : `${bestMove.left}-${bestMove.right}`;
            const flippedText = bestMove.flipped ? ' (مقلوبة)' : '';
            this.showMessage(`الكمبيوتر لعب قطعة ${originalNums}${flippedText}`);

            if (this.checkWinCondition()) {
                this.endGame();
                return;
            }

            this.switchTurn();
        } else if (this.boneyard.length > 0) {
            this.drawFromBoneyard('ai');
            this.showMessage('الكمبيوتر سحب قطعة من المخزون');
            setTimeout(() => this.aiTurn(), 500); // Try again after drawing
        } else {
            this.passTurn();
        }
    }

    // Remove tile from AI hand
    removeFromAIHand(tile) {
        const index = this.aiHand.findIndex(t => t.id === tile.id);
        if (index !== -1) {
            this.aiHand.splice(index, 1);
        }
    }

    // Draw from boneyard
    drawFromBoneyard(player) {
        if (this.boneyard.length === 0) return false;

        const drawnTile = this.boneyard.pop();
        
        if (player === 'player') {
            this.playerHand.push(drawnTile);
            this.renderPlayerHand();
        } else {
            this.aiHand.push(drawnTile);
            this.renderAIHand();
        }
        
        this.renderBoneyard();
        return true;
    }

    // Pass turn
    passTurn() {
        this.showMessage(`${this.currentPlayer === 'player' ? 'أنت' : 'الكمبيوتر'} مرر الدور`);
        this.switchTurn();
    }

    // Check win condition
    checkWinCondition() {
        return this.playerHand.length === 0 || this.aiHand.length === 0;
    }

    // End game
    endGame() {
        const winner = this.playerHand.length === 0 ? 'player' : 'ai';

        if (winner === 'player') {
            this.playerScore++;
            this.showMessage('مبروك! لقد فزت! 🎉');
        } else {
            this.aiScore++;
            this.showMessage('الكمبيوتر فاز! 🤖');
        }

        // Update statistics if available
        if (this.updateStats) {
            this.updateStats(winner);
        }

        this.updateScores();
        this.currentPlayer = null;
        this.updateUI();

        // Show game summary
        setTimeout(() => {
            const totalTiles = 7 - (winner === 'player' ? this.aiHand.length : this.playerHand.length);
            this.showMessage(`تم لعب ${totalTiles} قطعة في هذه الجولة`);
        }, 2000);
    }

    // Show hint
    showHint() {
        if (this.currentPlayer !== 'player') return;

        const playableTiles = this.playerHand.filter(tile => this.canPlaceTile(tile));
        
        if (playableTiles.length > 0) {
            this.showMessage(`يمكنك لعب ${playableTiles.length} قطعة`);
        } else {
            this.showMessage('لا توجد قطع يمكن لعبها. اسحب من المخزون أو مرر الدور');
        }
    }

    // Update UI elements
    updateUI() {
        const turnElement = document.getElementById('current-turn');
        const passButton = document.getElementById('pass-turn-btn');
        
        if (this.currentPlayer === 'player') {
            turnElement.textContent = 'دورك';
            turnElement.style.backgroundColor = '#4299e1';
            passButton.disabled = false;
        } else if (this.currentPlayer === 'ai') {
            turnElement.textContent = 'دور الكمبيوتر';
            turnElement.style.backgroundColor = '#e53e3e';
            passButton.disabled = true;
        } else {
            turnElement.textContent = 'انتهت اللعبة';
            turnElement.style.backgroundColor = '#38a169';
            passButton.disabled = true;
        }
    }

    // Update scores
    updateScores() {
        document.getElementById('player-score').textContent = this.playerScore;
        document.getElementById('ai-score').textContent = this.aiScore;
    }

    // Show message
    showMessage(message) {
        const messageElement = document.getElementById('game-message');
        messageElement.textContent = message;
        messageElement.classList.add('fade-in');
        
        setTimeout(() => {
            messageElement.classList.remove('fade-in');
        }, 3000);
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    const game = new DominoGame();

    // Add click handler for drawing from boneyard
    document.getElementById('boneyard').addEventListener('click', () => {
        if (game.currentPlayer === 'player' && game.boneyard.length > 0) {
            game.drawFromBoneyard('player');
            game.showMessage('سحبت قطعة من المخزون');
        }
    });

    // Add rules modal functionality
    const rulesBtn = document.createElement('button');
    rulesBtn.textContent = 'القواعد';
    rulesBtn.className = 'btn secondary';
    rulesBtn.addEventListener('click', () => {
        document.getElementById('rules-modal').style.display = 'block';
    });
    document.querySelector('.game-controls').appendChild(rulesBtn);

    // Close modal functionality
    document.querySelector('.close').addEventListener('click', () => {
        document.getElementById('rules-modal').style.display = 'none';
    });

    window.addEventListener('click', (e) => {
        const modal = document.getElementById('rules-modal');
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        switch(e.key) {
            case 'n':
            case 'N':
                if (e.ctrlKey) {
                    e.preventDefault();
                    game.initializeGame();
                }
                break;
            case 'h':
            case 'H':
                if (game.currentPlayer === 'player') {
                    game.showHint();
                }
                break;
            case ' ':
                if (game.currentPlayer === 'player') {
                    e.preventDefault();
                    game.passTurn();
                }
                break;
        }
    });

    // Add sound effects (optional - can be enabled if audio files are added)
    game.playSound = function(soundType) {
        // Placeholder for sound effects
        // You can add audio files and implement this later
        console.log(`Playing sound: ${soundType}`);
    };

    // Add animation effects
    game.animateTilePlacement = function(tileElement) {
        tileElement.classList.add('slide-in');
        setTimeout(() => {
            tileElement.classList.remove('slide-in');
        }, 300);
    };

    // Enhanced AI with difficulty levels
    game.aiDifficulty = 'medium'; // easy, medium, hard

    game.getAIMove = function() {
        const playableTiles = this.aiHand.filter(tile => this.canPlaceTile(tile));

        if (playableTiles.length === 0) return null;

        switch(this.aiDifficulty) {
            case 'easy':
                return playableTiles[Math.floor(Math.random() * playableTiles.length)];

            case 'medium':
                // Prefer tiles that block the player or have higher values
                const scoredTiles = playableTiles.map(tile => ({
                    tile,
                    score: tile.left + tile.right + (this.isBlockingTile(tile) ? 5 : 0)
                }));
                scoredTiles.sort((a, b) => b.score - a.score);
                return scoredTiles[0].tile;

            case 'hard':
                // Advanced AI logic - consider future moves
                return this.getBestAIMove(playableTiles);

            default:
                return playableTiles[Math.floor(Math.random() * playableTiles.length)];
        }
    };

    game.isBlockingTile = function(tile) {
        // Check if this tile would make it harder for the player
        const playerValues = new Set();
        this.playerHand.forEach(t => {
            playerValues.add(t.left);
            playerValues.add(t.right);
        });

        return !playerValues.has(tile.left) && !playerValues.has(tile.right);
    };

    game.getBestAIMove = function(playableTiles) {
        // Simple implementation - can be enhanced
        return playableTiles.reduce((best, current) => {
            const currentScore = current.left + current.right;
            const bestScore = best.left + best.right;
            return currentScore > bestScore ? current : best;
        });
    };

    // Add game statistics
    game.gameStats = {
        gamesPlayed: 0,
        playerWins: 0,
        aiWins: 0,
        totalTilesPlayed: 0
    };

    game.updateStats = function(winner) {
        this.gameStats.gamesPlayed++;
        if (winner === 'player') {
            this.gameStats.playerWins++;
        } else {
            this.gameStats.aiWins++;
        }
        this.saveStats();
    };

    game.saveStats = function() {
        localStorage.setItem('dominoGameStats', JSON.stringify(this.gameStats));
    };

    game.loadStats = function() {
        const saved = localStorage.getItem('dominoGameStats');
        if (saved) {
            this.gameStats = JSON.parse(saved);
        }
    };

    // Load saved statistics
    game.loadStats();

    // Add settings panel
    const settingsBtn = document.createElement('button');
    settingsBtn.textContent = 'الإعدادات';
    settingsBtn.className = 'btn secondary';
    settingsBtn.addEventListener('click', () => {
        game.showSettings();
    });
    document.querySelector('.game-controls').appendChild(settingsBtn);

    game.showSettings = function() {
        const settings = prompt(`اختر مستوى الصعوبة:
1 - سهل
2 - متوسط
3 - صعب

المستوى الحالي: ${this.aiDifficulty === 'easy' ? 'سهل' : this.aiDifficulty === 'medium' ? 'متوسط' : 'صعب'}`);

        switch(settings) {
            case '1':
                this.aiDifficulty = 'easy';
                this.showMessage('تم تغيير المستوى إلى سهل');
                break;
            case '2':
                this.aiDifficulty = 'medium';
                this.showMessage('تم تغيير المستوى إلى متوسط');
                break;
            case '3':
                this.aiDifficulty = 'hard';
                this.showMessage('تم تغيير المستوى إلى صعب');
                break;
        }
    };
});
