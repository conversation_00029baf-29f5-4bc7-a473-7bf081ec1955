/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.game-header {
    text-align: center;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.game-header h1 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 2.5em;
}

.score-board {
    display: flex;
    justify-content: center;
    gap: 40px;
    font-size: 1.2em;
    font-weight: bold;
}

.player-score {
    color: #2d5a87;
}

.ai-score {
    color: #c53030;
}

/* Game Status */
.game-status {
    text-align: center;
    margin-bottom: 20px;
}

.turn-indicator {
    background: #4299e1;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    display: inline-block;
    font-weight: bold;
    margin-bottom: 10px;
}

.message {
    font-size: 1.1em;
    color: #2d3748;
    background: rgba(255, 255, 255, 0.8);
    padding: 10px;
    border-radius: 10px;
    display: inline-block;
}

/* Game Board */
.game-board-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.12),
        0 4px 10px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    min-height: 300px;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.game-board {
    position: relative;
    min-height: 250px;
    border: 3px dashed #cbd5e0;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
    padding: 15px;
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.5) 0%,
        rgba(241, 245, 249, 0.3) 100%);
    transition: all 0.3s ease;
}

.board-center {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.start-message {
    color: #64748b;
    font-size: 1.3em;
    text-align: center;
    font-weight: 500;
    padding: 20px;
    background: linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(139, 92, 246, 0.1));
    border-radius: 12px;
    border: 2px dashed rgba(66, 153, 225, 0.3);
    animation: startMessageFloat 3s ease-in-out infinite;
}

@keyframes startMessageFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* Domino Tiles */
.domino-tile {
    width: 60px;
    height: 120px;
    cursor: pointer;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border: 3px solid #ffffff;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    background-color: #fefefe;
    /* Add subtle gradient for depth */
    background-image: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
}

.domino-tile:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.25),
        0 4px 10px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: #4299e1;
    background-image: linear-gradient(145deg, #ffffff 0%, #f0f8ff 100%);
}

.domino-tile.dragging {
    opacity: 0.8;
    transform: rotate(8deg) scale(1.1);
    box-shadow:
        0 12px 30px rgba(0, 0, 0, 0.3),
        0 6px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    border-color: #3182ce;
}

.domino-tile.horizontal {
    width: 120px;
    height: 60px;
    /* Rotate the image and stretch it to fill the horizontal container */
    transform: rotate(90deg);
    transform-origin: center center;
    background-size: 100% 100% !important;
    background-position: center center;
    transition: all 0.3s ease;
    border-radius: 12px;
    /* Enhanced shadow for horizontal tiles */
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.18),
        0 3px 6px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.domino-tile.horizontal.flipped {
    /* Additional rotation for flipped tiles to show correct orientation */
    transform: rotate(90deg) scaleX(-1);
    /* Add subtle highlight to indicate flipped state */
    border-color: #4299e1;
    background-image: linear-gradient(145deg, #f0f8ff 0%, #ffffff 100%);
}

.domino-tile.horizontal.flipped:hover {
    transform: rotate(90deg) translateY(-8px) scale(1.05) scaleX(-1);
}

.domino-tile.horizontal:hover {
    transform: rotate(90deg) translateY(-8px) scale(1.05);
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.25),
        0 5px 10px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.domino-tile.placed {
    cursor: default;
    /* Slightly different styling for placed tiles */
    border-color: #e2e8f0;
    background-image: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%);
    animation: placeAnimation 0.5s ease-out;
}

@keyframes placeAnimation {
    0% {
        transform: scale(1.2) rotate(10deg);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.1) rotate(5deg);
        opacity: 0.9;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

.domino-tile.back {
    background: linear-gradient(145deg, #4a5568 0%, #2d3748 50%, #1a202c 100%);
    border: 3px solid #1a202c;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 2px 4px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    /* Add pattern for back of tiles */
    background-image:
        linear-gradient(145deg, #4a5568 0%, #2d3748 50%, #1a202c 100%),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 2px,
            rgba(255, 255, 255, 0.05) 2px,
            rgba(255, 255, 255, 0.05) 4px
        );
}

/* Visual connection indicators */
.domino-tile.placed {
    position: relative;
}

.domino-tile.placed::after {
    content: '';
    position: absolute;
    background: linear-gradient(45deg, rgba(66, 153, 225, 0.4), rgba(66, 153, 225, 0.2));
    border-radius: 3px;
    z-index: -1;
    box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
    animation: connectionPulse 2s ease-in-out infinite;
}

@keyframes connectionPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

/* Connection indicators for horizontal tiles */
.domino-tile.horizontal.placed::after {
    width: 6px;
    height: 25px;
    top: 50%;
    left: -3px;
    transform: translateY(-50%);
}

/* Connection indicators for vertical tiles */
.domino-tile:not(.horizontal).placed::after {
    width: 25px;
    height: 6px;
    left: 50%;
    top: -3px;
    transform: translateX(-50%);
}

/* Player and AI Hands */
.player-section, .ai-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.player-section h3, .ai-section h3 {
    margin-bottom: 10px;
    color: #4a5568;
    text-align: center;
}

.hand {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    min-height: 80px;
    padding: 10px;
    border: 1px dashed #cbd5e0;
    border-radius: 10px;
}

.ai-hand .domino-tile {
    background: linear-gradient(45deg, #4a5568, #2d3748);
    border: 2px solid #1a202c;
}

/* Game Controls */
.game-controls {
    text-align: center;
    margin-bottom: 20px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.btn.primary {
    background: #4299e1;
    color: white;
}

.btn.primary:hover {
    background: #3182ce;
    transform: translateY(-2px);
}

.btn.secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn.secondary:hover {
    background: #cbd5e0;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Boneyard */
.boneyard-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.boneyard {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    justify-content: center;
    margin-top: 10px;
}

.boneyard .domino-tile {
    width: 30px;
    height: 60px;
    background: linear-gradient(145deg, #4a5568 0%, #2d3748 50%, #1a202c 100%);
    border: 2px solid #1a202c;
    border-radius: 6px;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 1px 2px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
    cursor: pointer;
    /* Add subtle pattern */
    background-image:
        linear-gradient(145deg, #4a5568 0%, #2d3748 50%, #1a202c 100%),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 1px,
            rgba(255, 255, 255, 0.03) 1px,
            rgba(255, 255, 255, 0.03) 2px
        );
}

.boneyard .domino-tile:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.4),
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    border-color: #4299e1;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border-radius: 15px;
    width: 80%;
    max-width: 500px;
    position: relative;
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 10px;
    left: 15px;
}

.close:hover {
    color: #000;
}

.rules-content h3 {
    color: #4a5568;
    margin: 15px 0 10px 0;
}

.rules-content ul {
    margin-right: 20px;
}

.rules-content li {
    margin-bottom: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-container {
        padding: 10px;
    }
    
    .domino-tile {
        width: 45px;
        height: 90px;
        border-radius: 8px;
        border-width: 2px;
        box-shadow:
            0 3px 6px rgba(0, 0, 0, 0.15),
            0 1px 3px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }
    
    .domino-tile.horizontal {
        width: 90px;
        height: 45px;
        /* Rotate the image and stretch it to fill the horizontal container on mobile */
        transform: rotate(90deg);
        transform-origin: center center;
        background-size: 100% 100% !important;
        background-position: center center;
        transition: transform 0.3s ease;
    }
    
    .score-board {
        gap: 20px;
        font-size: 1em;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
}

/* Drag and Drop Effects */
.drop-zone {
    border: 3px dashed #4299e1 !important;
    background: linear-gradient(45deg,
        rgba(66, 153, 225, 0.1) 0%,
        rgba(66, 153, 225, 0.05) 50%,
        rgba(66, 153, 225, 0.1) 100%);
    box-shadow:
        inset 0 0 20px rgba(66, 153, 225, 0.2),
        0 0 20px rgba(66, 153, 225, 0.3);
    animation: dropZonePulse 1.5s ease-in-out infinite;
}

@keyframes dropZonePulse {
    0%, 100% {
        box-shadow:
            inset 0 0 20px rgba(66, 153, 225, 0.2),
            0 0 20px rgba(66, 153, 225, 0.3);
    }
    50% {
        box-shadow:
            inset 0 0 30px rgba(66, 153, 225, 0.3),
            0 0 30px rgba(66, 153, 225, 0.4);
    }
}

.invalid-drop {
    border: 3px dashed #e53e3e !important;
    background: linear-gradient(45deg,
        rgba(229, 62, 62, 0.1) 0%,
        rgba(229, 62, 62, 0.05) 50%,
        rgba(229, 62, 62, 0.1) 100%);
    box-shadow:
        inset 0 0 20px rgba(229, 62, 62, 0.2),
        0 0 20px rgba(229, 62, 62, 0.3);
    animation: invalidDropShake 0.5s ease-in-out;
}

@keyframes invalidDropShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}
