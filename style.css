/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.game-header {
    text-align: center;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.game-header h1 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 2.5em;
}

.score-board {
    display: flex;
    justify-content: center;
    gap: 40px;
    font-size: 1.2em;
    font-weight: bold;
}

.player-score {
    color: #2d5a87;
}

.ai-score {
    color: #c53030;
}

/* Game Status */
.game-status {
    text-align: center;
    margin-bottom: 20px;
}

.turn-indicator {
    background: #4299e1;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    display: inline-block;
    font-weight: bold;
    margin-bottom: 10px;
}

.message {
    font-size: 1.1em;
    color: #2d3748;
    background: rgba(255, 255, 255, 0.8);
    padding: 10px;
    border-radius: 10px;
    display: inline-block;
}

/* Game Board */
.game-board-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.12),
        0 4px 10px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    min-height: 300px;
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.game-board {
    position: relative;
    min-height: 250px;
    border: 3px dashed #cbd5e0;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
    padding: 15px;
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.5) 0%,
        rgba(241, 245, 249, 0.3) 100%);
    transition: all 0.3s ease;
}

.board-center {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.start-message {
    color: #64748b;
    font-size: 1.3em;
    text-align: center;
    font-weight: 500;
    padding: 20px;
    background: linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(139, 92, 246, 0.1));
    border-radius: 12px;
    border: 2px dashed rgba(66, 153, 225, 0.3);
    animation: startMessageFloat 3s ease-in-out infinite;
}

@keyframes startMessageFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* Domino Tiles - Enhanced Beautiful Design */
.domino-tile {
    width: 70px;
    height: 140px;
    cursor: pointer;
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border: 4px solid #ffffff;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.15),
        0 4px 10px rgba(0, 0, 0, 0.1),
        inset 0 2px 4px rgba(255, 255, 255, 0.9),
        inset 0 -2px 4px rgba(0, 0, 0, 0.05);
    background-color: #ffffff;
    /* Beautiful gradient with subtle texture */
    background-image:
        linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%),
        radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(139, 92, 246, 0.03) 0%, transparent 50%);
    /* Add subtle border glow */
    outline: 1px solid rgba(59, 130, 246, 0.1);
    outline-offset: -1px;
}

.domino-tile:hover {
    transform: translateY(-12px) scale(1.08) rotateX(5deg);
    box-shadow:
        0 15px 40px rgba(59, 130, 246, 0.25),
        0 8px 20px rgba(0, 0, 0, 0.15),
        inset 0 2px 4px rgba(255, 255, 255, 0.95),
        inset 0 -2px 4px rgba(59, 130, 246, 0.1),
        0 0 0 2px rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
    background-image:
        linear-gradient(145deg, #ffffff 0%, #eff6ff 50%, #dbeafe 100%),
        radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.08) 0%, transparent 70%);
    outline: 2px solid rgba(59, 130, 246, 0.3);
    outline-offset: 2px;
}

.domino-tile.dragging {
    opacity: 0.9;
    transform: rotate(12deg) scale(1.15) translateZ(50px);
    box-shadow:
        0 20px 50px rgba(59, 130, 246, 0.4),
        0 10px 25px rgba(0, 0, 0, 0.25),
        inset 0 2px 4px rgba(255, 255, 255, 0.95),
        0 0 0 3px rgba(59, 130, 246, 0.4);
    z-index: 1000;
    border-color: #2563eb;
    background-image:
        linear-gradient(145deg, #ffffff 0%, #dbeafe 50%, #bfdbfe 100%),
        radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.12) 0%, transparent 70%);
    animation: dragGlow 0.6s ease-in-out infinite alternate;
}

@keyframes dragGlow {
    0% {
        box-shadow:
            0 20px 50px rgba(59, 130, 246, 0.4),
            0 10px 25px rgba(0, 0, 0, 0.25),
            0 0 0 3px rgba(59, 130, 246, 0.4);
    }
    100% {
        box-shadow:
            0 25px 60px rgba(59, 130, 246, 0.6),
            0 12px 30px rgba(0, 0, 0, 0.3),
            0 0 0 4px rgba(59, 130, 246, 0.6);
    }
}

.domino-tile.horizontal {
    width: 140px;
    height: 70px;
    /* Rotate the image and stretch it to fill the horizontal container */
    transform: rotate(90deg);
    transform-origin: center center;
    background-size: 100% 100% !important;
    background-position: center center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-radius: 16px;
    /* Enhanced shadow for horizontal tiles */
    box-shadow:
        0 8px 20px rgba(139, 92, 246, 0.15),
        0 4px 10px rgba(0, 0, 0, 0.12),
        inset 0 2px 4px rgba(255, 255, 255, 0.9),
        inset 0 -2px 4px rgba(139, 92, 246, 0.05);
    /* Special gradient for horizontal tiles */
    background-image:
        linear-gradient(145deg, #ffffff 0%, #faf5ff 50%, #f3e8ff 100%),
        radial-gradient(circle at 30% 70%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    outline: 1px solid rgba(139, 92, 246, 0.15);
}

.domino-tile.horizontal.flipped {
    /* Additional rotation for flipped tiles to show correct orientation */
    transform: rotate(90deg) scaleX(-1);
    /* Add beautiful highlight to indicate flipped state */
    border-color: #8b5cf6;
    background-image:
        linear-gradient(145deg, #faf5ff 0%, #f3e8ff 50%, #e9d5ff 100%),
        radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.08) 0%, transparent 70%);
    outline: 2px solid rgba(139, 92, 246, 0.25);
    box-shadow:
        0 8px 20px rgba(139, 92, 246, 0.25),
        0 4px 10px rgba(0, 0, 0, 0.12),
        inset 0 2px 4px rgba(255, 255, 255, 0.95),
        inset 0 -2px 4px rgba(139, 92, 246, 0.1);
}

.domino-tile.horizontal:hover {
    transform: rotate(90deg) translateY(-12px) scale(1.08);
    box-shadow:
        0 15px 35px rgba(139, 92, 246, 0.3),
        0 8px 18px rgba(0, 0, 0, 0.15),
        inset 0 2px 4px rgba(255, 255, 255, 0.95),
        0 0 0 2px rgba(139, 92, 246, 0.3);
}

.domino-tile.horizontal.flipped:hover {
    transform: rotate(90deg) translateY(-12px) scale(1.08) scaleX(-1);
    box-shadow:
        0 15px 35px rgba(139, 92, 246, 0.4),
        0 8px 18px rgba(0, 0, 0, 0.15),
        inset 0 2px 4px rgba(255, 255, 255, 0.95),
        0 0 0 3px rgba(139, 92, 246, 0.4);
}

.domino-tile.placed {
    cursor: default;
    /* Beautiful styling for placed tiles */
    border-color: #e2e8f0;
    background-image:
        linear-gradient(145deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%),
        radial-gradient(circle at 50% 50%, rgba(34, 197, 94, 0.03) 0%, transparent 70%);
    animation: placeAnimation 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    box-shadow:
        0 6px 15px rgba(34, 197, 94, 0.1),
        0 3px 8px rgba(0, 0, 0, 0.08),
        inset 0 1px 2px rgba(255, 255, 255, 0.9),
        inset 0 -1px 2px rgba(34, 197, 94, 0.05);
}

@keyframes placeAnimation {
    0% {
        transform: scale(1.4) rotate(15deg) translateY(-20px);
        opacity: 0.5;
        box-shadow:
            0 20px 40px rgba(34, 197, 94, 0.3),
            0 10px 20px rgba(0, 0, 0, 0.2);
    }
    50% {
        transform: scale(1.15) rotate(8deg) translateY(-5px);
        opacity: 0.8;
        box-shadow:
            0 12px 25px rgba(34, 197, 94, 0.2),
            0 6px 12px rgba(0, 0, 0, 0.15);
    }
    100% {
        transform: scale(1) rotate(0deg) translateY(0px);
        opacity: 1;
        box-shadow:
            0 6px 15px rgba(34, 197, 94, 0.1),
            0 3px 8px rgba(0, 0, 0, 0.08);
    }
}

.domino-tile.back {
    background: linear-gradient(145deg, #6366f1 0%, #4f46e5 30%, #4338ca 70%, #3730a3 100%);
    border: 4px solid #312e81;
    box-shadow:
        0 8px 20px rgba(67, 56, 202, 0.4),
        0 4px 10px rgba(0, 0, 0, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.15),
        inset 0 -2px 4px rgba(55, 48, 163, 0.3);
    /* Beautiful pattern for back of tiles */
    background-image:
        linear-gradient(145deg, #6366f1 0%, #4f46e5 30%, #4338ca 70%, #3730a3 100%),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 3px,
            rgba(255, 255, 255, 0.08) 3px,
            rgba(255, 255, 255, 0.08) 6px
        ),
        repeating-linear-gradient(
            -45deg,
            transparent,
            transparent 3px,
            rgba(255, 255, 255, 0.04) 3px,
            rgba(255, 255, 255, 0.04) 6px
        ),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    /* Add subtle glow */
    outline: 1px solid rgba(99, 102, 241, 0.3);
    outline-offset: 1px;
}

/* Visual connection indicators */
.domino-tile.placed {
    position: relative;
}

.domino-tile.placed::after {
    content: '';
    position: absolute;
    background: linear-gradient(45deg,
        rgba(34, 197, 94, 0.6) 0%,
        rgba(16, 185, 129, 0.4) 50%,
        rgba(5, 150, 105, 0.3) 100%);
    border-radius: 6px;
    z-index: -1;
    box-shadow:
        0 4px 8px rgba(34, 197, 94, 0.4),
        0 0 12px rgba(34, 197, 94, 0.3);
    animation: connectionPulse 3s ease-in-out infinite;
}

@keyframes connectionPulse {
    0%, 100% {
        opacity: 0.4;
        transform: scale(1);
        box-shadow:
            0 4px 8px rgba(34, 197, 94, 0.4),
            0 0 12px rgba(34, 197, 94, 0.3);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
        box-shadow:
            0 6px 12px rgba(34, 197, 94, 0.6),
            0 0 20px rgba(34, 197, 94, 0.5);
    }
}

/* Connection indicators for horizontal tiles */
.domino-tile.horizontal.placed::after {
    width: 8px;
    height: 30px;
    top: 50%;
    left: -4px;
    transform: translateY(-50%);
}

/* Connection indicators for vertical tiles */
.domino-tile:not(.horizontal).placed::after {
    width: 30px;
    height: 8px;
    left: 50%;
    top: -4px;
    transform: translateX(-50%);
}

/* Player and AI Hands */
.player-section, .ai-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.player-section h3, .ai-section h3 {
    margin-bottom: 10px;
    color: #4a5568;
    text-align: center;
}

.hand {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    min-height: 80px;
    padding: 10px;
    border: 1px dashed #cbd5e0;
    border-radius: 10px;
}

.ai-hand .domino-tile {
    background: linear-gradient(45deg, #4a5568, #2d3748);
    border: 2px solid #1a202c;
}

/* Game Controls */
.game-controls {
    text-align: center;
    margin-bottom: 20px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.btn.primary {
    background: #4299e1;
    color: white;
}

.btn.primary:hover {
    background: #3182ce;
    transform: translateY(-2px);
}

.btn.secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn.secondary:hover {
    background: #cbd5e0;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Boneyard */
.boneyard-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.boneyard {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    justify-content: center;
    margin-top: 10px;
}

.boneyard .domino-tile {
    width: 30px;
    height: 60px;
    background: linear-gradient(145deg, #4a5568 0%, #2d3748 50%, #1a202c 100%);
    border: 2px solid #1a202c;
    border-radius: 6px;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 1px 2px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
    cursor: pointer;
    /* Add subtle pattern */
    background-image:
        linear-gradient(145deg, #4a5568 0%, #2d3748 50%, #1a202c 100%),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 1px,
            rgba(255, 255, 255, 0.03) 1px,
            rgba(255, 255, 255, 0.03) 2px
        );
}

.boneyard .domino-tile:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.4),
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    border-color: #4299e1;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border-radius: 15px;
    width: 80%;
    max-width: 500px;
    position: relative;
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 10px;
    left: 15px;
}

.close:hover {
    color: #000;
}

.rules-content h3 {
    color: #4a5568;
    margin: 15px 0 10px 0;
}

.rules-content ul {
    margin-right: 20px;
}

.rules-content li {
    margin-bottom: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-container {
        padding: 10px;
    }
    
    .domino-tile {
        width: 55px;
        height: 110px;
        border-radius: 12px;
        border-width: 3px;
        box-shadow:
            0 6px 12px rgba(0, 0, 0, 0.15),
            0 3px 6px rgba(0, 0, 0, 0.1),
            inset 0 2px 4px rgba(255, 255, 255, 0.9),
            inset 0 -1px 2px rgba(0, 0, 0, 0.05);
    }
    
    .domino-tile.horizontal {
        width: 110px;
        height: 55px;
        /* Rotate the image and stretch it to fill the horizontal container on mobile */
        transform: rotate(90deg);
        transform-origin: center center;
        background-size: 100% 100% !important;
        background-position: center center;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border-radius: 12px;
    }
    
    .score-board {
        gap: 20px;
        font-size: 1em;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
}

/* Drag and Drop Effects */
.drop-zone {
    border: 3px dashed #4299e1 !important;
    background: linear-gradient(45deg,
        rgba(66, 153, 225, 0.1) 0%,
        rgba(66, 153, 225, 0.05) 50%,
        rgba(66, 153, 225, 0.1) 100%);
    box-shadow:
        inset 0 0 20px rgba(66, 153, 225, 0.2),
        0 0 20px rgba(66, 153, 225, 0.3);
    animation: dropZonePulse 1.5s ease-in-out infinite;
}

@keyframes dropZonePulse {
    0%, 100% {
        box-shadow:
            inset 0 0 20px rgba(66, 153, 225, 0.2),
            0 0 20px rgba(66, 153, 225, 0.3);
    }
    50% {
        box-shadow:
            inset 0 0 30px rgba(66, 153, 225, 0.3),
            0 0 30px rgba(66, 153, 225, 0.4);
    }
}

.invalid-drop {
    border: 3px dashed #e53e3e !important;
    background: linear-gradient(45deg,
        rgba(229, 62, 62, 0.1) 0%,
        rgba(229, 62, 62, 0.05) 50%,
        rgba(229, 62, 62, 0.1) 100%);
    box-shadow:
        inset 0 0 20px rgba(229, 62, 62, 0.2),
        0 0 20px rgba(229, 62, 62, 0.3);
    animation: invalidDropShake 0.5s ease-in-out;
}

@keyframes invalidDropShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}
