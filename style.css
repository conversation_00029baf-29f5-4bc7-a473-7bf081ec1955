/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.game-header {
    text-align: center;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.game-header h1 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 2.5em;
}

.score-board {
    display: flex;
    justify-content: center;
    gap: 40px;
    font-size: 1.2em;
    font-weight: bold;
}

.player-score {
    color: #2d5a87;
}

.ai-score {
    color: #c53030;
}

/* Game Status */
.game-status {
    text-align: center;
    margin-bottom: 20px;
}

.turn-indicator {
    background: #4299e1;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    display: inline-block;
    font-weight: bold;
    margin-bottom: 10px;
}

.message {
    font-size: 1.1em;
    color: #2d3748;
    background: rgba(255, 255, 255, 0.8);
    padding: 10px;
    border-radius: 10px;
    display: inline-block;
}

/* Game Board */
.game-board-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    min-height: 300px;
}

.game-board {
    position: relative;
    min-height: 250px;
    border: 2px dashed #cbd5e0;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 5px;
    padding: 10px;
}

.board-center {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.start-message {
    color: #a0aec0;
    font-size: 1.2em;
    text-align: center;
}

/* Domino Tiles */
.domino-tile {
    width: 60px;
    height: 120px;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border: 2px solid #e2e8f0;
}

.domino-tile:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.domino-tile.dragging {
    opacity: 0.7;
    transform: rotate(5deg);
}

.domino-tile.horizontal {
    width: 120px;
    height: 60px;
    /* Rotate the image and stretch it to fill the horizontal container */
    transform: rotate(90deg);
    transform-origin: center center;
    background-size: 100% 100% !important;
    background-position: center center;
    transition: transform 0.3s ease;
}

.domino-tile.placed {
    cursor: default;
}

.domino-tile.back {
    background: linear-gradient(45deg, #4a5568, #2d3748);
    border: 2px solid #1a202c;
}

/* Player and AI Hands */
.player-section, .ai-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.player-section h3, .ai-section h3 {
    margin-bottom: 10px;
    color: #4a5568;
    text-align: center;
}

.hand {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    min-height: 80px;
    padding: 10px;
    border: 1px dashed #cbd5e0;
    border-radius: 10px;
}

.ai-hand .domino-tile {
    background: linear-gradient(45deg, #4a5568, #2d3748);
    border: 2px solid #1a202c;
}

/* Game Controls */
.game-controls {
    text-align: center;
    margin-bottom: 20px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.btn.primary {
    background: #4299e1;
    color: white;
}

.btn.primary:hover {
    background: #3182ce;
    transform: translateY(-2px);
}

.btn.secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn.secondary:hover {
    background: #cbd5e0;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Boneyard */
.boneyard-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.boneyard {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    justify-content: center;
    margin-top: 10px;
}

.boneyard .domino-tile {
    width: 30px;
    height: 60px;
    background: linear-gradient(45deg, #4a5568, #2d3748);
    border: 1px solid #1a202c;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border-radius: 15px;
    width: 80%;
    max-width: 500px;
    position: relative;
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 10px;
    left: 15px;
}

.close:hover {
    color: #000;
}

.rules-content h3 {
    color: #4a5568;
    margin: 15px 0 10px 0;
}

.rules-content ul {
    margin-right: 20px;
}

.rules-content li {
    margin-bottom: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-container {
        padding: 10px;
    }
    
    .domino-tile {
        width: 45px;
        height: 90px;
    }
    
    .domino-tile.horizontal {
        width: 90px;
        height: 45px;
        /* Rotate the image and stretch it to fill the horizontal container on mobile */
        transform: rotate(90deg);
        transform-origin: center center;
        background-size: 100% 100% !important;
        background-position: center center;
        transition: transform 0.3s ease;
    }
    
    .score-board {
        gap: 20px;
        font-size: 1em;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
}

/* Drag and Drop Effects */
.drop-zone {
    border: 3px dashed #4299e1 !important;
    background-color: rgba(66, 153, 225, 0.1);
}

.invalid-drop {
    border: 3px dashed #e53e3e !important;
    background-color: rgba(229, 62, 62, 0.1);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}
