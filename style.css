/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.game-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 10px;
    gap: 15px;
}

/* Top Bar Layout */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    padding: 20px 30px;
    border-radius: 20px;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.12),
        0 4px 10px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.game-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
}

.game-header h1 {
    color: #4a5568;
    margin: 0;
    font-size: 2.2em;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.turn-indicator {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9em;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

/* Score Panel */
.score-panel {
    display: flex;
    align-items: center;
    gap: 20px;
}

.score-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    padding: 15px 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.8);
    min-width: 80px;
}

.player-card {
    border-left: 4px solid #10b981;
}

.ai-card {
    border-left: 4px solid #ef4444;
}

.score-label {
    font-size: 0.8em;
    color: #64748b;
    font-weight: 600;
    margin-bottom: 5px;
}

.score-value {
    font-size: 2em;
    font-weight: 700;
    color: #1e293b;
    line-height: 1;
}

.score-subtitle {
    font-size: 0.7em;
    color: #94a3b8;
    margin-top: 2px;
}

.vs-divider {
    font-size: 1.2em;
    font-weight: 700;
    color: #64748b;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    padding: 10px 15px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Status Bar */
.status-bar {
    text-align: center;
    margin-bottom: 10px;
}

.message {
    font-size: 1.1em;
    color: #1e293b;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    padding: 12px 24px;
    border-radius: 25px;
    display: inline-block;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
    font-weight: 500;
    min-height: 20px;
}

/* Main Game Area Layout */
.main-game-area {
    display: grid;
    grid-template-columns: 200px 1fr 200px;
    gap: 20px;
    flex: 1;
    min-height: 400px;
}

.left-sidebar, .right-sidebar {
    display: flex;
    flex-direction: column;
}

.center-board {
    display: flex;
    flex-direction: column;
}

/* Game Board */
.board-header {
    text-align: center;
    margin-bottom: 15px;
}

.board-header h3 {
    color: #4a5568;
    margin: 0;
    font-size: 1.3em;
    font-weight: 600;
}

.game-board-container {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    border-radius: 20px;
    padding: 20px;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.12),
        0 4px 10px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.8);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.game-board {
    position: relative;
    flex: 1;
    min-height: 300px;
    border: 3px dashed #cbd5e0;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
    padding: 20px;
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.5) 0%,
        rgba(241, 245, 249, 0.3) 100%);
    transition: all 0.3s ease;
    overflow: auto;
}

.board-center {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.start-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    color: #64748b;
    font-size: 1.2em;
    text-align: center;
    font-weight: 500;
    padding: 30px;
    background: linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(139, 92, 246, 0.1));
    border-radius: 20px;
    border: 2px dashed rgba(66, 153, 225, 0.3);
    animation: startMessageFloat 3s ease-in-out infinite;
}

.start-icon {
    font-size: 3em;
    animation: iconSpin 4s ease-in-out infinite;
}

.start-text {
    line-height: 1.5;
}

@keyframes startMessageFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

@keyframes iconSpin {
    0%, 100% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(5deg) scale(1.1); }
    50% { transform: rotate(0deg) scale(1); }
    75% { transform: rotate(-5deg) scale(1.1); }
}

/* Domino Tiles - Enhanced Beautiful Design */
.domino-tile {
    width: 70px;
    height: 140px;
    cursor: pointer;
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border: 4px solid #ffffff;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.15),
        0 4px 10px rgba(0, 0, 0, 0.1),
        inset 0 2px 4px rgba(255, 255, 255, 0.9),
        inset 0 -2px 4px rgba(0, 0, 0, 0.05);
    background-color: #ffffff;
    /* Beautiful gradient with subtle texture */
    background-image:
        linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%),
        radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(139, 92, 246, 0.03) 0%, transparent 50%);
    /* Add subtle border glow */
    outline: 1px solid rgba(59, 130, 246, 0.1);
    outline-offset: -1px;
}

.domino-tile:hover {
    transform: translateY(-12px) scale(1.08) rotateX(5deg);
    box-shadow:
        0 15px 40px rgba(59, 130, 246, 0.25),
        0 8px 20px rgba(0, 0, 0, 0.15),
        inset 0 2px 4px rgba(255, 255, 255, 0.95),
        inset 0 -2px 4px rgba(59, 130, 246, 0.1),
        0 0 0 2px rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
    background-image:
        linear-gradient(145deg, #ffffff 0%, #eff6ff 50%, #dbeafe 100%),
        radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.08) 0%, transparent 70%);
    outline: 2px solid rgba(59, 130, 246, 0.3);
    outline-offset: 2px;
}

.domino-tile.dragging {
    opacity: 0.9;
    transform: rotate(12deg) scale(1.15) translateZ(50px);
    box-shadow:
        0 20px 50px rgba(59, 130, 246, 0.4),
        0 10px 25px rgba(0, 0, 0, 0.25),
        inset 0 2px 4px rgba(255, 255, 255, 0.95),
        0 0 0 3px rgba(59, 130, 246, 0.4);
    z-index: 1000;
    border-color: #2563eb;
    background-image:
        linear-gradient(145deg, #ffffff 0%, #dbeafe 50%, #bfdbfe 100%),
        radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.12) 0%, transparent 70%);
    animation: dragGlow 0.6s ease-in-out infinite alternate;
}

@keyframes dragGlow {
    0% {
        box-shadow:
            0 20px 50px rgba(59, 130, 246, 0.4),
            0 10px 25px rgba(0, 0, 0, 0.25),
            0 0 0 3px rgba(59, 130, 246, 0.4);
    }
    100% {
        box-shadow:
            0 25px 60px rgba(59, 130, 246, 0.6),
            0 12px 30px rgba(0, 0, 0, 0.3),
            0 0 0 4px rgba(59, 130, 246, 0.6);
    }
}

.domino-tile.horizontal {
    width: 140px;
    height: 70px;
    /* Rotate the image and stretch it to fill the horizontal container */
    transform: rotate(90deg);
    transform-origin: center center;
    background-size: 100% 100% !important;
    background-position: center center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-radius: 16px;
    /* Enhanced shadow for horizontal tiles */
    box-shadow:
        0 8px 20px rgba(139, 92, 246, 0.15),
        0 4px 10px rgba(0, 0, 0, 0.12),
        inset 0 2px 4px rgba(255, 255, 255, 0.9),
        inset 0 -2px 4px rgba(139, 92, 246, 0.05);
    /* Special gradient for horizontal tiles */
    background-image:
        linear-gradient(145deg, #ffffff 0%, #faf5ff 50%, #f3e8ff 100%),
        radial-gradient(circle at 30% 70%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    outline: 1px solid rgba(139, 92, 246, 0.15);
}

.domino-tile.horizontal.flipped {
    /* Additional rotation for flipped tiles to show correct orientation */
    transform: rotate(90deg) scaleX(-1);
    /* Add beautiful highlight to indicate flipped state */
    border-color: #8b5cf6;
    background-image:
        linear-gradient(145deg, #faf5ff 0%, #f3e8ff 50%, #e9d5ff 100%),
        radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.08) 0%, transparent 70%);
    outline: 2px solid rgba(139, 92, 246, 0.25);
    box-shadow:
        0 8px 20px rgba(139, 92, 246, 0.25),
        0 4px 10px rgba(0, 0, 0, 0.12),
        inset 0 2px 4px rgba(255, 255, 255, 0.95),
        inset 0 -2px 4px rgba(139, 92, 246, 0.1);
}

.domino-tile.horizontal:hover {
    transform: rotate(90deg) translateY(-12px) scale(1.08);
    box-shadow:
        0 15px 35px rgba(139, 92, 246, 0.3),
        0 8px 18px rgba(0, 0, 0, 0.15),
        inset 0 2px 4px rgba(255, 255, 255, 0.95),
        0 0 0 2px rgba(139, 92, 246, 0.3);
}

.domino-tile.horizontal.flipped:hover {
    transform: rotate(90deg) translateY(-12px) scale(1.08) scaleX(-1);
    box-shadow:
        0 15px 35px rgba(139, 92, 246, 0.4),
        0 8px 18px rgba(0, 0, 0, 0.15),
        inset 0 2px 4px rgba(255, 255, 255, 0.95),
        0 0 0 3px rgba(139, 92, 246, 0.4);
}

.domino-tile.placed {
    cursor: default;
    /* Dramatic transformation for placed tiles */
    border: 3px solid #10b981;
    background: linear-gradient(145deg,
        #ecfdf5 0%,
        #d1fae5 25%,
        #a7f3d0 50%,
        #6ee7b7 75%,
        #34d399 100%);
    animation: placeAnimation 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    box-shadow:
        0 10px 30px rgba(16, 185, 129, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.15),
        inset 0 2px 4px rgba(255, 255, 255, 0.9),
        inset 0 -2px 4px rgba(16, 185, 129, 0.2),
        0 0 0 1px rgba(16, 185, 129, 0.3);
    /* Add special effects */
    position: relative;
    transform: scale(0.95);
    filter: brightness(1.1) saturate(1.2);
}

/* Add glowing border effect */
.domino-tile.placed::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        #10b981, #34d399, #6ee7b7, #34d399, #10b981);
    border-radius: inherit;
    z-index: -1;
    animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

/* Special styling for placed horizontal tiles */
.domino-tile.horizontal.placed {
    border-color: #8b5cf6;
    background: linear-gradient(145deg,
        #f3e8ff 0%,
        #e9d5ff 25%,
        #d8b4fe 50%,
        #c084fc 75%,
        #a855f7 100%);
    box-shadow:
        0 10px 30px rgba(139, 92, 246, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.15),
        inset 0 2px 4px rgba(255, 255, 255, 0.9),
        inset 0 -2px 4px rgba(139, 92, 246, 0.2),
        0 0 0 1px rgba(139, 92, 246, 0.3);
    filter: brightness(1.1) saturate(1.3);
}

.domino-tile.horizontal.placed::before {
    background: linear-gradient(45deg,
        #8b5cf6, #a855f7, #c084fc, #a855f7, #8b5cf6);
}

/* Special styling for flipped placed tiles */
.domino-tile.horizontal.flipped.placed {
    border-color: #f59e0b;
    background: linear-gradient(145deg,
        #fef3c7 0%,
        #fde68a 25%,
        #fcd34d 50%,
        #fbbf24 75%,
        #f59e0b 100%);
    box-shadow:
        0 10px 30px rgba(245, 158, 11, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.15),
        inset 0 2px 4px rgba(255, 255, 255, 0.9),
        inset 0 -2px 4px rgba(245, 158, 11, 0.2),
        0 0 0 1px rgba(245, 158, 11, 0.3);
    filter: brightness(1.2) saturate(1.4);
}

.domino-tile.horizontal.flipped.placed::before {
    background: linear-gradient(45deg,
        #f59e0b, #fbbf24, #fcd34d, #fbbf24, #f59e0b);
}

/* Add sparkle effect for placed tiles */
.domino-tile.placed::after {
    content: '✨';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.2em;
    animation: sparkle 2s ease-in-out infinite;
    z-index: 10;
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0.5) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(180deg);
    }
}

/* Special styling for first and last tiles */
.domino-tile.placed:first-child {
    border-color: #ef4444;
    background: linear-gradient(145deg,
        #fef2f2 0%,
        #fecaca 25%,
        #fca5a5 50%,
        #f87171 75%,
        #ef4444 100%);
    box-shadow:
        0 12px 35px rgba(239, 68, 68, 0.5),
        0 6px 18px rgba(0, 0, 0, 0.18),
        inset 0 2px 4px rgba(255, 255, 255, 0.9),
        inset 0 -2px 4px rgba(239, 68, 68, 0.3),
        0 0 0 2px rgba(239, 68, 68, 0.4);
    animation: placeAnimation 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55),
               firstTilePulse 3s ease-in-out infinite 1.2s;
}

.domino-tile.placed:first-child::before {
    background: linear-gradient(45deg,
        #ef4444, #f87171, #fca5a5, #f87171, #ef4444);
}

.domino-tile.placed:first-child::after {
    content: '🔥';
}

.domino-tile.placed:last-child {
    border-color: #3b82f6;
    background: linear-gradient(145deg,
        #eff6ff 0%,
        #dbeafe 25%,
        #bfdbfe 50%,
        #93c5fd 75%,
        #60a5fa 100%);
    box-shadow:
        0 12px 35px rgba(59, 130, 246, 0.5),
        0 6px 18px rgba(0, 0, 0, 0.18),
        inset 0 2px 4px rgba(255, 255, 255, 0.9),
        inset 0 -2px 4px rgba(59, 130, 246, 0.3),
        0 0 0 2px rgba(59, 130, 246, 0.4);
    animation: placeAnimation 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55),
               lastTilePulse 3s ease-in-out infinite 1.2s;
}

.domino-tile.placed:last-child::before {
    background: linear-gradient(45deg,
        #3b82f6, #60a5fa, #93c5fd, #60a5fa, #3b82f6);
}

.domino-tile.placed:last-child::after {
    content: '⭐';
}

@keyframes firstTilePulse {
    0%, 100% {
        transform: scale(0.95);
        filter: brightness(1.1) saturate(1.2);
    }
    50% {
        transform: scale(1);
        filter: brightness(1.3) saturate(1.5);
    }
}

@keyframes lastTilePulse {
    0%, 100% {
        transform: scale(0.95);
        filter: brightness(1.1) saturate(1.2);
    }
    50% {
        transform: scale(1);
        filter: brightness(1.3) saturate(1.5);
    }
}

@keyframes placeAnimation {
    0% {
        transform: scale(1.8) rotate(25deg) translateY(-40px);
        opacity: 0.3;
        filter: brightness(2) saturate(2) hue-rotate(45deg);
        box-shadow:
            0 30px 60px rgba(16, 185, 129, 0.6),
            0 15px 30px rgba(0, 0, 0, 0.3),
            0 0 0 5px rgba(16, 185, 129, 0.5);
    }
    25% {
        transform: scale(1.4) rotate(15deg) translateY(-20px);
        opacity: 0.6;
        filter: brightness(1.8) saturate(1.8) hue-rotate(30deg);
        box-shadow:
            0 25px 50px rgba(16, 185, 129, 0.5),
            0 12px 25px rgba(0, 0, 0, 0.25),
            0 0 0 4px rgba(16, 185, 129, 0.4);
    }
    50% {
        transform: scale(1.2) rotate(8deg) translateY(-10px);
        opacity: 0.8;
        filter: brightness(1.5) saturate(1.5) hue-rotate(15deg);
        box-shadow:
            0 20px 40px rgba(16, 185, 129, 0.4),
            0 10px 20px rgba(0, 0, 0, 0.2),
            0 0 0 3px rgba(16, 185, 129, 0.3);
    }
    75% {
        transform: scale(1.05) rotate(3deg) translateY(-2px);
        opacity: 0.9;
        filter: brightness(1.3) saturate(1.3) hue-rotate(5deg);
        box-shadow:
            0 15px 30px rgba(16, 185, 129, 0.3),
            0 8px 15px rgba(0, 0, 0, 0.15),
            0 0 0 2px rgba(16, 185, 129, 0.2);
    }
    100% {
        transform: scale(0.95) rotate(0deg) translateY(0px);
        opacity: 1;
        filter: brightness(1.1) saturate(1.2);
        box-shadow:
            0 10px 30px rgba(16, 185, 129, 0.4),
            0 5px 15px rgba(0, 0, 0, 0.15),
            inset 0 2px 4px rgba(255, 255, 255, 0.9),
            inset 0 -2px 4px rgba(16, 185, 129, 0.2),
            0 0 0 1px rgba(16, 185, 129, 0.3);
    }
}

.domino-tile.back {
    background: linear-gradient(145deg, #6366f1 0%, #4f46e5 30%, #4338ca 70%, #3730a3 100%);
    border: 4px solid #312e81;
    box-shadow:
        0 8px 20px rgba(67, 56, 202, 0.4),
        0 4px 10px rgba(0, 0, 0, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.15),
        inset 0 -2px 4px rgba(55, 48, 163, 0.3);
    /* Beautiful pattern for back of tiles */
    background-image:
        linear-gradient(145deg, #6366f1 0%, #4f46e5 30%, #4338ca 70%, #3730a3 100%),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 3px,
            rgba(255, 255, 255, 0.08) 3px,
            rgba(255, 255, 255, 0.08) 6px
        ),
        repeating-linear-gradient(
            -45deg,
            transparent,
            transparent 3px,
            rgba(255, 255, 255, 0.04) 3px,
            rgba(255, 255, 255, 0.04) 6px
        ),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    /* Add subtle glow */
    outline: 1px solid rgba(99, 102, 241, 0.3);
    outline-offset: 1px;
}

/* Visual connection indicators - Updated for new placed tile design */
.domino-tile.placed {
    position: relative;
}

/* Remove the old connection indicators since we have new sparkle effects */

/* New connection system with colored lines */
.domino-tile.placed + .domino-tile.placed::before {
    content: '';
    position: absolute;
    background: linear-gradient(90deg,
        rgba(16, 185, 129, 0.8) 0%,
        rgba(34, 197, 94, 0.6) 50%,
        rgba(16, 185, 129, 0.8) 100%);
    z-index: -2;
    animation: connectionFlow 2s ease-in-out infinite;
}

.domino-tile.horizontal.placed + .domino-tile.placed::before,
.domino-tile.placed + .domino-tile.horizontal.placed::before {
    width: 15px;
    height: 4px;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
}

.domino-tile:not(.horizontal).placed + .domino-tile:not(.horizontal).placed::before {
    width: 4px;
    height: 15px;
    left: 50%;
    top: -10px;
    transform: translateX(-50%);
}

@keyframes connectionFlow {
    0%, 100% {
        opacity: 0.6;
        box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
    }
    50% {
        opacity: 1;
        box-shadow: 0 0 15px rgba(16, 185, 129, 0.8);
    }
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.6);
}

.section-header h3 {
    margin: 0;
    color: #4a5568;
    font-size: 1.1em;
    font-weight: 600;
}

.tile-count {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Sidebar Sections */
.ai-section, .boneyard-section {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 15px;
    padding: 15px;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.8);
    height: fit-content;
}

/* Hand Layouts */
.hand {
    display: flex;
    gap: 8px;
    padding: 15px;
    border: 2px dashed rgba(203, 213, 224, 0.6);
    border-radius: 12px;
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.3) 0%,
        rgba(241, 245, 249, 0.2) 100%);
    min-height: 80px;
}

.vertical-hand {
    flex-direction: column;
    align-items: center;
    max-height: 400px;
    overflow-y: auto;
}

.horizontal-hand {
    flex-wrap: wrap;
    justify-content: center;
}

/* Bottom Area */
.bottom-area {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.player-section {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 20px;
    padding: 20px;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.12),
        0 4px 10px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

/* Boneyard Specific */
.vertical-stack {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(35px, 1fr));
    gap: 5px;
    max-height: 300px;
    overflow-y: auto;
}

.boneyard-hint {
    text-align: center;
    font-size: 0.8em;
    color: #64748b;
    margin-top: 10px;
    font-style: italic;
}

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 20px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-radius: 20px;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 15px;
    font-size: 0.9em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
}

.btn-icon {
    font-size: 1.1em;
}

.btn-text {
    font-weight: 600;
}

.btn.primary {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
}

.btn.primary:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.btn.secondary {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #475569;
    border-color: rgba(203, 213, 224, 0.5);
}

.btn.secondary:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
    transform: translateY(-2px) scale(1.02);
    border-color: rgba(148, 163, 184, 0.8);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover {
    transform: none !important;
}

/* Boneyard */
.boneyard-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.boneyard {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    justify-content: center;
    margin-top: 10px;
}

.boneyard .domino-tile {
    width: 30px;
    height: 60px;
    background: linear-gradient(145deg, #4a5568 0%, #2d3748 50%, #1a202c 100%);
    border: 2px solid #1a202c;
    border-radius: 6px;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 1px 2px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
    cursor: pointer;
    /* Add subtle pattern */
    background-image:
        linear-gradient(145deg, #4a5568 0%, #2d3748 50%, #1a202c 100%),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 1px,
            rgba(255, 255, 255, 0.03) 1px,
            rgba(255, 255, 255, 0.03) 2px
        );
}

.boneyard .domino-tile:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.4),
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    border-color: #4299e1;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border-radius: 15px;
    width: 80%;
    max-width: 500px;
    position: relative;
}

.close {
    color: #aaa;
    float: left;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 10px;
    left: 15px;
}

.close:hover {
    color: #000;
}

.rules-content h3 {
    color: #4a5568;
    margin: 15px 0 10px 0;
}

.rules-content ul {
    margin-right: 20px;
}

.rules-content li {
    margin-bottom: 5px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-game-area {
        grid-template-columns: 180px 1fr 180px;
        gap: 15px;
    }

    .top-bar {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .game-header {
        align-items: center;
    }
}

@media (max-width: 768px) {
    .game-container {
        padding: 8px;
        gap: 10px;
    }

    .main-game-area {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
        gap: 15px;
    }

    .left-sidebar {
        order: 3;
    }

    .center-board {
        order: 1;
    }

    .right-sidebar {
        order: 2;
    }

    .ai-section, .boneyard-section {
        padding: 12px;
    }

    .vertical-hand {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        max-height: none;
    }

    .vertical-stack {
        grid-template-columns: repeat(auto-fit, minmax(30px, 1fr));
        max-height: 150px;
    }

    .domino-tile {
        width: 45px;
        height: 90px;
        border-radius: 10px;
        border-width: 2px;
    }

    .domino-tile.horizontal {
        width: 90px;
        height: 45px;
        border-radius: 10px;
    }

    .game-header h1 {
        font-size: 1.8em;
    }

    .score-panel {
        gap: 15px;
    }

    .score-card {
        padding: 12px 16px;
        min-width: 70px;
    }

    .game-controls {
        flex-direction: column;
        gap: 10px;
    }

    .btn {
        padding: 10px 16px;
        font-size: 0.85em;
    }
}

@media (max-width: 480px) {
    .top-bar {
        padding: 15px 20px;
    }

    .game-header h1 {
        font-size: 1.5em;
    }

    .score-value {
        font-size: 1.5em;
    }

    .domino-tile {
        width: 40px;
        height: 80px;
    }

    .domino-tile.horizontal {
        width: 80px;
        height: 40px;
    }

    .game-board {
        min-height: 200px;
        padding: 15px;
    }
}

/* Drag and Drop Effects */
.drop-zone {
    border: 3px dashed #4299e1 !important;
    background: linear-gradient(45deg,
        rgba(66, 153, 225, 0.1) 0%,
        rgba(66, 153, 225, 0.05) 50%,
        rgba(66, 153, 225, 0.1) 100%);
    box-shadow:
        inset 0 0 20px rgba(66, 153, 225, 0.2),
        0 0 20px rgba(66, 153, 225, 0.3);
    animation: dropZonePulse 1.5s ease-in-out infinite;
}

@keyframes dropZonePulse {
    0%, 100% {
        box-shadow:
            inset 0 0 20px rgba(66, 153, 225, 0.2),
            0 0 20px rgba(66, 153, 225, 0.3);
    }
    50% {
        box-shadow:
            inset 0 0 30px rgba(66, 153, 225, 0.3),
            0 0 30px rgba(66, 153, 225, 0.4);
    }
}

.invalid-drop {
    border: 3px dashed #e53e3e !important;
    background: linear-gradient(45deg,
        rgba(229, 62, 62, 0.1) 0%,
        rgba(229, 62, 62, 0.05) 50%,
        rgba(229, 62, 62, 0.1) 100%);
    box-shadow:
        inset 0 0 20px rgba(229, 62, 62, 0.2),
        0 0 20px rgba(229, 62, 62, 0.3);
    animation: invalidDropShake 0.5s ease-in-out;
}

@keyframes invalidDropShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}
